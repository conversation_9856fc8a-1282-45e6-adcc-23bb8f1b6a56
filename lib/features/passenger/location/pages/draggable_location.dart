import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/blocs/current_latlng_address/current_lat_lng_address_bloc.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/app_map_pin.dart';
import 'package:safari_yatri/core/widget/current_location_navigator_button.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/location/blocs/current_location_navigator/current_location_navigator_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/is_coming_from_draggable/is_coming_from_draggable_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:safari_yatri/features/passenger/location/models/location_picking_type.dart';
import 'package:safari_yatri/features/passenger/location/models/passenger_location.dart';

import '../../../../core/theme/app_styles.dart';

class DraggableLocationPickerMap extends StatefulWidget {
  final LocationPickingType type;
  const DraggableLocationPickerMap({super.key, required this.type});

  @override
  State<DraggableLocationPickerMap> createState() =>
      _DraggableLocationPickerMapState();
}

class _DraggableLocationPickerMapState
    extends State<DraggableLocationPickerMap> {
  LatLng? _currentLocation;
  LatLngWithAddress? _pickupLocation;
  LatLngWithAddress? _destinationLocation;
  late final AppMapPickerController mapPickerController;
  final Completer<GoogleMapController> _mapController = Completer();
  CameraPosition? _cameraPosition;
  bool _isLoading = false;
  bool _shouldPopTwice = false;
  @override
  void initState() {
    super.initState();
    mapPickerController = AppMapPickerController();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<
        CurrentLocationNavigatorCubit,
        CurrentLocationNavigatorState
      >(
        listener: (context, state) {
          state.whenOrNull(
            loaded: (data) {
              _currentLocation = data;
              _mapController.future.then((controller) async {
                // _isProgrammaticCameraMovement =
                //     true; // Set flag before camera movement
                await controller.animateCamera(
                  CameraUpdate.newCameraPosition(
                    CameraPosition(target: data, zoom: kMapInitialZoom),
                  ),
                );
                // Add a small delay to ensure animation completes
                await Future.delayed(Duration(milliseconds: 300));
                // _isProgrammaticCameraMovement =
                //     false; // Reset flag after movement
              });
            },
          );
        },
        child: BlocConsumer<PassengerRouteBloc, PassengerRouteState>(
          listener: (context, state) {
            state.whenOrNull(
              loaded: (data) {
                if (_shouldPopTwice) {
                  ///hami chaii draggle bata aako ho haina ko lagi gareko
                  sl<IsComingFromDraggableCubit>().isComingFromDraggable(true);
                  context.goNamed(AppRoutesName.passengerHome);
                }
              },
            );
          },

          builder: (context, state) {
            state.whenOrNull(
              loading: () {
                _isLoading = true;
              },
              loaded: (data) {
                _isLoading = false;
                if (widget.type.isPickup) {
                  _pickupLocation = data.pickupLocation;
                } else {
                  if (widget.type.isDestination && widget.type.index != null) {
                    _destinationLocation =
                        data.dropoffLocations[widget.type.index!];
                  }

                  if (widget.type.isDestination && widget.type.index == null) {
                    _destinationLocation =
                        data.currentPassengerLocationWithAddress;
                  }
                }

                _currentLocation =
                    widget.type.isPickup
                        ? LatLng(
                          _pickupLocation!.latitude,
                          _pickupLocation!.longitude,
                        )
                        : LatLng(
                          _destinationLocation!.latitude,
                          _destinationLocation!.longitude,
                        );

                WidgetsBinding.instance.addPostFrameCallback((_) {
                  setState(() {});
                });
              },
            );
            return _currentLocation != null
                ? Stack(
                  children: [
                    BlocBuilder<
                      CurrentLatLngAddressBloc,
                      CurrentLatLngAddressState
                    >(
                      builder: (context, state) {
                        return AppMapPicker(
                          iconWidget: SvgPicture.asset(
                            ImageConstant.mapMarker,
                            width: 50,
                            height: 50,
                          ),
                          mapPickerController: mapPickerController,
                          bottomPadding:
                              0, // No bottom padding for draggable location picker
                          child: CustomGoogleMap(
                            myLocationEnabled: true,
                            zoomControlsEnabled: false,
                            mapToolbarEnabled: false,
                            myLocationButtonEnabled: false,
                            mapType: MapType.normal,
                            initialCameraPosition: CameraPosition(
                              target: _currentLocation!,
                              zoom: kMapInitialZoom,
                            ),
                            onMapCreated:
                                (controller) =>
                                    _mapController.complete(controller),
                            onCameraMoveStarted: _onCameraMoveStarted,
                            onCameraMove:
                                (position) => _cameraPosition = position,
                            onCameraIdle: _onCameraIdle,
                          ),
                        );
                      },
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppStyles.space32,
                        horizontal: AppStyles.space12,
                      ),
                      child: CustomBackButton(),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppStyles.space32,
                        horizontal: AppStyles.space12,
                      ),
                      child: CustomBackButton(),
                    ),

                    Positioned(
                      bottom: 16,
                      left: 16,
                      right: 16,
                      child: BlocBuilder<MapMovementCubit, MapMovementState>(
                        builder: (context, state) {
                          return Column(
                            children: [
                              Align(
                                alignment: Alignment.bottomRight,
                                child: Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: CurrentLocationNavigatorButton(),
                                ),
                              ),
                              Gap(8),
                              SafeArea(
                                child: CustomButtonPrimary(
                                  isLoading: _isLoading,
                                  onPressed:
                                      state == MapMovementState.moving
                                          ? null
                                          : _onDone,
                                  title: 'Done',
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                )
                : SizedBox();
          },
        ),
      ),
    );
  }

  void _onCameraMoveStarted() {
    sl<PickupLocationPickingStatusCubit>().startPicking();
    sl<MapMovementCubit>().startMoving();
    mapPickerController.mapMoving?.call();
  }

  Future<void> _onCameraIdle() async {
    sl<PickupLocationPickingStatusCubit>().stopPicking();
    _cameraPosition?.target != null
        ? sl<CurrentLatLngAddressBloc>().add(
          CurrentLatLngAddressEvent.get(position: _cameraPosition!.target),
        )
        : null;
    sl<MapMovementCubit>().stopMoving();

    mapPickerController.mapFinishedMoving?.call();
  }

  void _onDone() {
    _shouldPopTwice = true;
    if (widget.type.isPickup) {
      sl<PassengerRouteBloc>().add(
        PassengerRouteEvent.pickUpLocation(position: _cameraPosition!.target),
      );
    } else {
      sl<PickupLocationPickingStatusCubit>().disable();

      if (widget.type.isDestination && widget.type.index != null) {
        sl<PassengerRouteBloc>().add(
          PassengerRouteEvent.updateDestinationLocation(
            position: _cameraPosition!.target,
            index: widget.type.index!,
          ),
        );
      } else {
        sl<PassengerRouteBloc>().add(
          PassengerRouteEvent.destinationLocation(
            position: _cameraPosition!.target,
          ),
        );
      }
    }
  }
}
