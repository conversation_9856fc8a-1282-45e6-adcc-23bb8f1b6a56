import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/blocs/current_latlng_address/current_lat_lng_address_bloc.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/utils/ride_map_helper.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/app_map_pin.dart';
import 'package:safari_yatri/features/location/blocs/current_location_navigator/current_location_navigator_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/is_coming_from_draggable/is_coming_from_draggable_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:safari_yatri/features/passenger/location/models/passenger_location.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';

class PassengerMap extends StatefulWidget {
  const PassengerMap({super.key});
  @override
  State<PassengerMap> createState() => _PassengerMapState();
}

class _PassengerMapState extends State<PassengerMap> {
  LatLng? _currentLocation;
  late final AppMapPickerController mapPickerController;
  final Completer<GoogleMapController> _mapController = Completer();
  CameraPosition? _cameraPosition;
  bool _isDisablePicking = false;
  bool _isCurrentLocationLoading = false;
  List<LatLng> _points = [];
  List<LatLngWithAddress> _markersPoints = [];
  Set<Polyline> _polylines = {};
  Set<Marker> _markers = {};
  bool isCameraMoveCompletedOnce = false;
  bool _shouldShowLocationPickingMarker = true;
  // Animation related variables
  Timer? _polylineAnimationTimer;
  int _currentPolylineIndex = 0;

  // New flag to distinguish programmatic camera moves from user moves
  bool _isProgrammaticCameraMove = false;

  @override
  void initState() {
    super.initState();
    mapPickerController = AppMapPickerController();
  }

  @override
  void dispose() {
    _polylineAnimationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<
        PickupLocationPickingStatusCubit,
        PickupLocationPickingStatusState
      >(
        builder: (context, pickState) {
          _isDisablePicking =
              pickState == PickupLocationPickingStatusState.disable;
          return BlocBuilder<IsComingFromDraggableCubit, bool>(
            builder: (context, isComplatedFromDraggable) {
              return BlocConsumer<PassengerRouteBloc, PassengerRouteState>(
                listener: (context, state) => _passengerRouteListener(state),
                builder: (context, state) {
                  _shouldShowLocationPickingMarker = state.maybeWhen(
                    loaded: (data) => data.dropoffLocations.isEmpty,
                    orElse: () => true,
                  );
                  _passengerRouteBuilder(state, isComplatedFromDraggable);
                  return _currentLocation != null
                      ? AppMapPicker(
                        showDot: !_isDisablePicking,
                        bottomPadding: kPassengerHomeBottomSheetHeight,
                        iconWidget:
                            _isDisablePicking
                                ? null
                                : !_shouldShowLocationPickingMarker
                                ? null
                                : SvgPicture.asset(
                                  ImageConstant.mapMarker,
                                  width: 50,
                                  height: 50,
                                ),
                        mapPickerController: mapPickerController,
                        child: _currentLocationMoverListener(
                          child: _buildGoogleMap(),
                        ),
                      )
                      : _isCurrentLocationLoading
                      ? const SizedBox()
                      : const CustomGoogleMap(
                        zoomControlsEnabled: false,
                        myLocationButtonEnabled: false,
                        mapType: MapType.normal,
                        initialCameraPosition: CameraPosition(
                          target: LatLng(27.7172, 85.3240),
                          zoom: kMapInitialZoom,
                        ),
                      );
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildGoogleMap() {
    return BlocBuilder<PassengerRouteBloc, PassengerRouteState>(
      builder: (context, state) {
        final EdgeInsets mapPadding =
            state.maybeWhen(
              loaded: (data) {
                if (data.dropoffLocations.isEmpty) {
                  return EdgeInsets.zero;
                }
                return const EdgeInsets.only(
                  bottom: kPassengerHomeBottomSheetHeight,
                );
              },
              orElse: () => EdgeInsets.zero,
            ) ??
            EdgeInsets.zero;
        return CustomGoogleMap(
          padding: EdgeInsets.only(bottom: kPassengerHomeBottomSheetHeight),
          polylines: _polylines,
          markers: _markers,
          myLocationEnabled: true,
          zoomControlsEnabled: false,
          myLocationButtonEnabled: false,
          mapToolbarEnabled: false,
          mapType: MapType.normal,
          initialCameraPosition: CameraPosition(
            target: _currentLocation!,
            zoom: kMapInitialZoom,
          ),
          onMapCreated: (controller) {
            _mapController.complete(controller);
          },
          // Use a combined listener for camera move started
          onCameraMoveStarted: () {
            if (_isDisablePicking) {
              _onMapMovementStarted();
            } else {
              _onCameraMoveStarted();
            }
          },
          onCameraMove: (position) => _cameraPosition = position,
          // Use a combined listener for camera idle
          onCameraIdle: () {
            if (_isDisablePicking) {
              _onMapMovementStopped();
            } else {
              _onCameraIdle();
            }
          },
        );
      },
    );
  }

  void _passengerRouteBuilder(
    PassengerRouteState state,
    bool isComplatedFromDraggable,
  ) {
    state.whenOrNull(
      loaded: (data) {
        if (isComplatedFromDraggable && !isCameraMoveCompletedOnce) {
          isCameraMoveCompletedOnce = true;
          _mapController.future.then((controller) async {
            _isProgrammaticCameraMove = true; // Set flag
            await controller.moveCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(
                  target: LatLng(
                    data.pickupLocation.latitude,
                    data.pickupLocation.longitude,
                  ),
                  zoom: kMapInitialZoom,
                ),
              ),
            );
            // Reset flag after a short delay to ensure onCameraIdle is called first
            Future.delayed(const Duration(milliseconds: 500), () {
              _isProgrammaticCameraMove = false;
            });
            await Future.delayed(const Duration(milliseconds: 1000));
            isCameraMoveCompletedOnce = false;
            sl<IsComingFromDraggableCubit>().isComingFromDraggable(false);
          });
        }
      },
    );
  }

  void _passengerRouteListener(PassengerRouteState state) {
    state.whenOrNull(
      loading: () {
        _isCurrentLocationLoading = true;
      },
      failure: (error) {
        _isCurrentLocationLoading = false;
      },
      loaded: (data) {
        _isCurrentLocationLoading = false;
        _currentLocation = data.currentLocation;

        _clearPoints();
        _points = data.polylinePoints;
        _markersPoints = [data.pickupLocation, ...data.dropoffLocations];

        _animatePolyline(_points);

        if (data.dropoffLocations.isNotEmpty) {
          _setMarkers();
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              // Ensure this zoom is also marked as programmatic
              _isProgrammaticCameraMove = true;
              _zoomToFitMarkers(_points).then((_) {
                // Reset flag after zoom completes and a small delay
                Future.delayed(const Duration(milliseconds: 500), () {
                  _isProgrammaticCameraMove = false;
                });
              });
            }
          });
        }
      },
    );
  }

  void _clearPoints() {
    _points.clear();
    _markersPoints.clear();
    _polylines.clear();
    _markers.clear();
  }

  BlocConsumer<CurrentLocationNavigatorCubit, CurrentLocationNavigatorState>
  _currentLocationMoverListener({required Widget child}) {
    return BlocConsumer<
      CurrentLocationNavigatorCubit,
      CurrentLocationNavigatorState
    >(
      listener: (context, state) {
        state.maybeWhen(
          loaded: (data) async {
            final controller = await _mapController.future;
            _isProgrammaticCameraMove = true; // Set flag

            // Calculate offset to center the location under the fixed pin
            final offsetTarget = _calculateOffsetForCenterPin(data);

            await controller.animateCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(target: offsetTarget, zoom: kMapInitialZoom),
              ),
            );
            // Reset flag after a short delay to ensure onCameraIdle is called first
            Future.delayed(const Duration(milliseconds: 500), () {
              _isProgrammaticCameraMove = false;
            });
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        return child;
      },
    );
  }

  /// Calculates the camera target position to center a location under the fixed pin
  /// accounting for the bottom UI padding
  LatLng _calculateOffsetForCenterPin(LatLng targetLocation) {
    // Calculate the offset needed to position the target under the center pin
    // The pin is positioned at the visual center of the visible map area
    final bottomPaddingOffset = kPassengerHomeBottomSheetHeight / 2;

    // Convert the bottom padding to latitude offset
    // Approximate conversion: 1 degree latitude ≈ 111,000 meters
    // Screen pixel to meter conversion varies by zoom level
    // At zoom level 17, approximately 1 pixel ≈ 1.2 meters
    final latitudeOffset = (bottomPaddingOffset * 1.2) / 111000;

    return LatLng(
      targetLocation.latitude + latitudeOffset,
      targetLocation.longitude,
    );
  }

  /// Calculates the actual location under the center pin when user taps/moves the map
  /// This is the inverse of _calculateOffsetForCenterPin
  LatLng _calculateLocationUnderCenterPin(LatLng cameraTarget) {
    // Calculate the reverse offset to get the actual location under the pin
    final bottomPaddingOffset = kPassengerHomeBottomSheetHeight / 2;
    final latitudeOffset = (bottomPaddingOffset * 1.2) / 111000;

    return LatLng(
      cameraTarget.latitude - latitudeOffset,
      cameraTarget.longitude,
    );
  }

  void _setMarkers() async {
    _markers.clear();
    if (_markersPoints.isEmpty) return;

    _markers = await AppMapHelper.generateRouteMarkers(
      _markersPoints.map((e) => LatLng(e.latitude, e.longitude)).toList(),
    );

    setState(() {});
  }

  void _setPolylines(List<LatLng> subPoints) {
    _polylines.clear();
    if (subPoints.length < 2) return;

    _polylines = AppMapHelper.createPolyline(subPoints);

    setState(() {});
  }

  void _animatePolyline(List<LatLng> points) {
    _polylineAnimationTimer?.cancel();
    _currentPolylineIndex = 0;

    const duration = Duration(milliseconds: 150);
    _polylineAnimationTimer = Timer.periodic(duration, (timer) {
      if (_currentPolylineIndex < points.length) {
        final subPoints = points.sublist(0, _currentPolylineIndex + 1);
        _setPolylines(subPoints);
        _currentPolylineIndex++;
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _zoomToFitMarkers(List<LatLng> locations) async {
    if (locations.isEmpty) return;
    double minLat = locations.first.latitude;
    double maxLat = locations.first.latitude;
    double minLng = locations.first.longitude;
    double maxLng = locations.first.longitude;

    for (final location in locations) {
      minLat = min(minLat, location.latitude);
      maxLat = max(maxLat, location.latitude);
      minLng = min(minLng, location.longitude);
      maxLng = max(maxLng, location.longitude);
    }

    final bounds = LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );

    final controller = await _mapController.future;
    await controller.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, kMapPadding),
    );
  }

  void _onMapMovementStarted() {
    sl<PickupLocationPickingStatusCubit>().startPicking();
  }

  void _onMapMovementStopped() {
    sl<PickupLocationPickingStatusCubit>().stopPicking();
  }

  void _onCameraMoveStarted() {
    // Only trigger UI updates for user-initiated camera moves
    // This prevents UI flickering during programmatic camera movements
    if (!_isProgrammaticCameraMove) {
      sl<MapMovementCubit>().startMoving();
      mapPickerController.mapMoving?.call();
    }
  }

  Future<void> _onCameraIdle() async {
    // Only dispatch pickup location event if it's not a programmatic move
    // and if picking is enabled (i.e., not _isDisablePicking).
    // _isDisablePicking is true when the bottom sheet is open or when
    // the user is not actively picking a location.
    if (_cameraPosition != null &&
        !_isProgrammaticCameraMove &&
        !_isDisablePicking) {
      // Calculate the actual location under the center pin
      final actualLocation = _calculateLocationUnderCenterPin(
        _cameraPosition!.target,
      );

      sl<CurrentLatLngAddressBloc>().add(
        CurrentLatLngAddressEvent.get(position: actualLocation),
      );
      sl<PassengerRouteBloc>().add(
        PassengerRouteEvent.pickUpLocation(position: actualLocation),
      );
    }

    // Always update UI state, but only for user-initiated moves
    sl<PickupLocationPickingStatusCubit>().stopPicking();
    if (!_isProgrammaticCameraMove) {
      sl<MapMovementCubit>().stopMoving();
      mapPickerController.mapFinishedMoving?.call();
    }
  }
}
