import 'package:flutter_test/flutter_test.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';

/// Test suite for map positioning calculations
/// Tests the inDrive-style map interaction fixes
void main() {
  group('Map Positioning Tests', () {
    test('should calculate correct offset for center pin positioning', () {
      // Test the offset calculation logic used in _calculateOffsetForCenterPin
      const targetLocation = LatLng(27.7172, 85.3240); // Kathmandu coordinates
      const bottomPaddingOffset = kPassengerHomeBottomSheetHeight / 2; // 180px
      const latitudeOffset = (bottomPaddingOffset * 1.2) / 111000; // ~0.00194
      
      final offsetLocation = LatLng(
        targetLocation.latitude + latitudeOffset,
        targetLocation.longitude,
      );
      
      // The offset should move the location slightly north (positive latitude)
      expect(offsetLocation.latitude, greaterThan(targetLocation.latitude));
      expect(offsetLocation.longitude, equals(targetLocation.longitude));
      
      // The offset should be approximately 0.00194 degrees
      final actualOffset = offsetLocation.latitude - targetLocation.latitude;
      expect(actualOffset, closeTo(0.00194, 0.0001));
    });

    test('should calculate correct location under center pin', () {
      // Test the reverse calculation used in _calculateLocationUnderCenterPin
      const cameraTarget = LatLng(27.7172, 85.3240);
      const bottomPaddingOffset = kPassengerHomeBottomSheetHeight / 2;
      const latitudeOffset = (bottomPaddingOffset * 1.2) / 111000;
      
      final actualLocation = LatLng(
        cameraTarget.latitude - latitudeOffset,
        cameraTarget.longitude,
      );
      
      // The actual location should be south of the camera target
      expect(actualLocation.latitude, lessThan(cameraTarget.latitude));
      expect(actualLocation.longitude, equals(cameraTarget.longitude));
    });

    test('should have symmetric offset calculations', () {
      // Test that offset and reverse offset are symmetric
      const originalLocation = LatLng(27.7172, 85.3240);
      const bottomPaddingOffset = kPassengerHomeBottomSheetHeight / 2;
      const latitudeOffset = (bottomPaddingOffset * 1.2) / 111000;
      
      // Apply offset (for centering current location)
      final offsetLocation = LatLng(
        originalLocation.latitude + latitudeOffset,
        originalLocation.longitude,
      );
      
      // Apply reverse offset (for getting location under pin)
      final reversedLocation = LatLng(
        offsetLocation.latitude - latitudeOffset,
        offsetLocation.longitude,
      );
      
      // Should get back to original location (within floating point precision)
      expect(reversedLocation.latitude, closeTo(originalLocation.latitude, 0.000001));
      expect(reversedLocation.longitude, closeTo(originalLocation.longitude, 0.000001));
    });

    test('should calculate correct pin position for different bottom paddings', () {
      const screenHeight = 800.0;
      
      // Test with no bottom padding (traditional center)
      const noPadding = 0.0;
      final noPaddingVisibleHeight = screenHeight - noPadding;
      final noPaddingPinPosition = screenHeight - (noPaddingVisibleHeight * 0.5) - 10;
      expect(noPaddingPinPosition, equals(390.0)); // (800 - 400 - 10)
      
      // Test with passenger home bottom padding
      const withPadding = kPassengerHomeBottomSheetHeight; // 360px
      final withPaddingVisibleHeight = screenHeight - withPadding;
      final withPaddingPinPosition = screenHeight - (withPaddingVisibleHeight * 0.5) - 10;
      expect(withPaddingPinPosition, equals(570.0)); // (800 - 220 - 10)
      
      // The pin should be positioned lower (higher bottom value) with padding
      expect(withPaddingPinPosition, greaterThan(noPaddingPinPosition));
    });

    test('should validate bottom sheet height constant', () {
      // Ensure the constant is reasonable for UI layout
      expect(kPassengerHomeBottomSheetHeight, equals(360.0));
      expect(kPassengerHomeBottomSheetHeight, greaterThan(200.0)); // Minimum reasonable height
      expect(kPassengerHomeBottomSheetHeight, lessThan(500.0)); // Maximum reasonable height
    });
  });

  group('Map Interaction Validation', () {
    test('should validate latitude offset is reasonable for user experience', () {
      // The offset should be noticeable but not excessive
      const bottomPaddingOffset = kPassengerHomeBottomSheetHeight / 2;
      const latitudeOffset = (bottomPaddingOffset * 1.2) / 111000;
      
      // Convert to meters for validation (1 degree latitude ≈ 111,000 meters)
      final offsetInMeters = latitudeOffset * 111000;
      
      // Should be approximately 216 meters (180px * 1.2)
      expect(offsetInMeters, closeTo(216.0, 1.0));
      
      // Should be reasonable for map interaction (not too small, not too large)
      expect(offsetInMeters, greaterThan(100.0)); // At least 100m offset
      expect(offsetInMeters, lessThan(500.0)); // At most 500m offset
    });
  });
}
